package models

import (
	"deskcrm/helpers"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// DeerDataLUDeerSpecialLPC 小鹿数据LU特殊LPC表模型
// 对应PHP中的Dao_Deer_DeerDataLUDeerSpecialLPC
type DeerDataLUDeerSpecialLPC struct {
	ID                            int64  `gorm:"column:id;primaryKey" json:"id"`
	CourseId                      int64  `gorm:"column:course_id" json:"courseId"`
	LessonId                      int64  `gorm:"column:lesson_id" json:"lessonId"`
	StudentUid                    int64  `gorm:"column:student_uid" json:"studentUid"`
	DeerEloquenceHomeworkLevel    int64  `gorm:"column:deer_eloquence_homework_level" json:"deerEloquenceHomeworkLevel"`
	DeerEloquenceHomeworkStatus   string `gorm:"column:deer_eloquence_homework_status" json:"deerEloquenceHomeworkStatus"`
	DeerProgrammingHomeworkLevel  int64  `gorm:"column:deer_programming_homework_level" json:"deerProgrammingHomeworkLevel"`
	DeerProgrammingHomeworkStatus string `gorm:"column:deer_programming_homework_status" json:"deerProgrammingHomeworkStatus"`
	ZbkDeleteFlag                 int64  `gorm:"column:zbk_delete_flag" json:"zbkDeleteFlag"`
	ZbkUpdateTime                 int64  `gorm:"column:zbk_update_time" json:"zbkUpdateTime"`
	UpdateTime                    int64  `gorm:"column:update_time" json:"updateTime"`
}

// TableName 指定表名
func (DeerDataLUDeerSpecialLPC) TableName() string {
	return "tblDeerDataLUDeerSpecialLPC"
}

// deerDataLUDeerSpecialLPCDao 小鹿数据DAO
type deerDataLUDeerSpecialLPCDao struct{}

// GetListByStudentLessons 根据学生ID和章节ID列表获取小鹿数据
// 对应PHP中的getListByConds方法
func (d *deerDataLUDeerSpecialLPCDao) GetListByStudentLessons(ctx *gin.Context, studentUid int64, lessonIds []int64) ([]DeerDataLUDeerSpecialLPC, error) {
	if studentUid <= 0 || len(lessonIds) == 0 {
		return []DeerDataLUDeerSpecialLPC{}, nil
	}

	var result []DeerDataLUDeerSpecialLPC

	// 构建查询条件 - 使用deer_workbench数据库
	db := helpers.MysqlClient.WithContext(ctx)
	err := db.Table("deer_workbench.tblDeerDataLUDeerSpecialLPC").
		Where("student_uid = ?", studentUid).
		Where("lesson_id IN ?", lessonIds).
		Where("zbk_delete_flag = 0"). // 排除已删除的记录
		Find(&result).Error

	if err != nil {
		zlog.Warnf(ctx, "GetListByStudentLessons failed: %v", err)
		return nil, err
	}

	zlog.Infof(ctx, "GetListByStudentLessons found %d records for student %d", len(result), studentUid)
	return result, nil
}

// DeerDataLUDeerSpecialLPCDao 全局实例
var DeerDataLUDeerSpecialLPCDao = &deerDataLUDeerSpecialLPCDao{}
