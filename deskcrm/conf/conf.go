package conf

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/cos"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/redis"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/server/http"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"golang.org/x/time/rate"
)

const App = "deskcrm"

// DefaultMysql mysql资源服务的默认服务名
const DefaultMysql = "bzr_fudao"

const DefaultRedis = "default"

const DuXueScMysql = "duxuesc"

const LpcActiveMysql = "lpcactive"

// ShadowServiceName 影子资源服务的默认服务名
const ShadowServiceName = "shadow"

const defaultJWTExpirationHours = 72

// 配置文件对应的全局变量
var (
	BasicConf TBasic
	API       TApi
	RConf     ResourceConf
	Custom    TCustom
)

// TBasic 基础配置,对应config.yaml
type TBasic struct {
	Pprof  base.PprofConfig
	Log    zlog.LogConfig
	Server http.ServerConfig
	// ....业务可扩展其他简单的配置
}

// TApi 对应 api.yaml
type TApi struct {
	ArkGo             *base.ApiClient `yaml:"arkgo"`
	AssistantDesk     *base.ApiClient `yaml:"assistantdesk"`
	AssistantDeskGo   *base.ApiClient `yaml:"assistantdeskgo"`
	DataProxy         *base.ApiClient `yaml:"dataproxy"`
	ZbCore            *base.ApiClient `yaml:"zbcore"`
	Tower             *base.ApiClient `yaml:"tower"`
	ZbCoreDal         *base.ApiClient `yaml:"zbcoredal"`
	DalGo             *base.ApiClient `yaml:"dalgo"`
	ZbCoreDau         *base.ApiClient `yaml:"zbcoredau"`
	ZbCoreDat         *base.ApiClient `yaml:"zbcoredat"`
	ZbCoreDas         *base.ApiClient `yaml:"zbcoredas"`
	ZbCoreDar         *base.ApiClient `yaml:"zbcoredar"`
	AssistantCourseGo *base.ApiClient `yaml:"assistantcoursego"`
	Mercury           *base.ApiClient `yaml:"mercury"`
	Mesh              *base.ApiClient `yaml:"mesh"`
	UserProfile       *base.ApiClient `yaml:"userprofile"`
	Allocate          *base.ApiClient `yaml:"allocate"`
	Moat              *base.ApiClient `yaml:"moat"`
	CourseTransGo     *base.ApiClient `yaml:"coursetransgo"`
	InClass           *base.ApiClient `yaml:"inclass"`
	KpStaff           *base.ApiClient `yaml:"kpstaff"`
	JxReport          *base.ApiClient `yaml:"jxreport"`
	JwBiz             *base.ApiClient `yaml:"jwbiz"`
	IOTOpenAPI        *base.ApiClient `yaml:"iotopenapi"`
	Su                *base.ApiClient `yaml:"su"`
	ExamCore          *base.ApiClient `yaml:"examcore"`
	GenKe             *base.ApiClient `yaml:"genke"`
	Billing           *base.ApiClient `yaml:"billing"`
	One               *base.ApiClient `yaml:"one"`
	Scpt              *base.ApiClient `yaml:"scpt"`
	GoodsPlatform     *base.ApiClient `yaml:"goodsplatform"`
	AfterPlat         *base.ApiClient `yaml:"afterplat"`
	LearningPlan      *base.ApiClient `yaml:"learningplan"`
	LpcDuxuesc        *base.ApiClient `yaml:"lpcduxuesc"`
	Achilles          *base.ApiClient `yaml:"achilles-v3"`
	Tag               *base.ApiClient `yaml:"tag"`
	Muse              *base.ApiClient `yaml:"muse"`
	TouchMisGo        *base.ApiClient `yaml:"touchmisgo"`
	TouchMis          *base.ApiClient `yaml:"touchmis"`
	Location          *base.ApiClient `yaml:"location"`
	JxExamUI          *base.ApiClient `yaml:"jxexamui"`
	FrontCourse       *base.ApiClient `yaml:"frontcoursenew"`
	PcAssistant       *base.ApiClient `yaml:"pcassistant"`
	JxDaScore         *base.ApiClient `yaml:"jxdascore"`
	Writereport       *base.ApiClient `yaml:"writereport"`
}

// rate单机限流配置
type RateItem struct {
	Limit  rate.Limit `yaml:"limit"`  // 每秒写入数，浮点型(float64),根据这个计数生产token的间隔时间，并发控制数
	Bursts int        `yaml:"bursts"` // 最大的token数目，也就是桶的容量，初始化时时满容量的，最大并发数
}

// TCustom custom.yaml
type TCustom struct {
	Rate map[string]RateItem `yaml:"rate"` // 单机限流令牌桶配置
	Mesh Mesh                `yaml:"mesh"`
}

type Mesh struct {
	AppId            string  `yaml:"appId"`
	ModuleId         string  `yaml:"moduleId"`
	FudaoProductLine string  `yaml:"fudaoProductLine"`
	LaxinProductLine string  `yaml:"laxinProductLine"`
	GroupId          []int64 `yaml:"groupId"`
}
type WordConvertConfig struct {
	ClientId             int    `yaml:"client_id" json:"client_id"`
	ClientSecret         string `yaml:"client_secret" json:"client_secret"`
	PlId                 string `yaml:"pl_id" json:"pl_id"`
	ProdType             string `yaml:"prod_type" json:"prod_type"`
	OrderSource          int    `yaml:"order_source" json:"order_source"`
	Type                 string `yaml:"type" json:"type"`
	WordStorageDirectory string `yaml:"word_storage_directory" json:"word_storage_directory"`
}

// JWTConfig represents an application jwt configuration.
type JWTConfig struct {
	// JWT signing key. required.
	SigningKey string `yaml:"signingKey"`
	// JWT expiration in hours. Defaults to 72 hours (3 days)
	Expiration int `yaml:"expiration"`
}

// ResourceConf 对应 resource.yaml
type ResourceConf struct {
	Redis   map[string]redis.RedisConf
	Mysql   map[string]base.MysqlConf
	Elastic map[string]base.ElasticClientConfig
	Cos     map[string]cos.BucketConfig
	Rmq     rmq.RmqConfig
	RmqNew  rmq.RmqConfig `yaml:"rmqNew"` // rmq集群迁移期间使用，迁移完成后删掉
}

func InitConf() {
	// 加载通用基础配置（必须）
	env.LoadConf("config.yaml", env.SubConfMount, &BasicConf)

	// 加载api调用相关配置（optional）
	env.LoadConf("api.yaml", env.SubConfMount, &API)

	// 加载资源类配置（optional）
	env.LoadConf("resource.yaml", env.SubConfMount, &RConf)

	// 加载业务类(需要通过配置中心可修改的业务类配置)配置 （optional）
	// ... 加载更多配置
	env.LoadConf("custom.yaml", env.SubConfMount, &Custom)
}

func GetAppName() string {
	return App
}
