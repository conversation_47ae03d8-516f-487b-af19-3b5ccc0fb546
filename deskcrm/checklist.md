# PHP方法迁移方舟规则清单
| num | key | funcName | status | comment |
|------|--------|----------|----------|--------------|
| 1 | lessonId | GetLessonId | verified | ✅ 重新验证通过 - 详见verification_report_GetLessonId.md |
| 2 | type | GetType | verified | ✅ 验证通过 |
| 3 | playType | GetPlayType | verified | ✅ 验证通过 |
| 4 | inclassTime | GetInclassTime | verified | ✅ 验证通过 |
| 5 | stopTime | GetStopTime | verified | ✅ 验证通过 |
| 6 | lessonName | GetLessonName | verified | ✅ 验证通过 |
| 7 | startTime | GetStartTime | verified | ✅ 验证通过 | 
| 8 | preview | GetPreview | verified | ✅ 完全修复 - 格式、数组结构、isPreviewFinish逻辑 |
| 9 | attend | GetAttendData | verified | ✅ 验证通过 - 逻辑与PHP一致，正确处理LBP、请假、时长格式化 |
| 10 | playback | GetPlayback | verified | ✅ 验证通过 - 逻辑与PHP一致，正确处理t007Tag、录播、时长格式化 |
| 11 | playbackv1 | GetPlaybackOnlineTimeV1 | verified | ✅ 修复格式化逻辑 - 确保0值返回"0min"与PHP一致 |
| 12 | lbpAttendDuration | GetLbpAttendDuration | verified | ✅ 修复格式化逻辑 - 确保0值也格式化与PHP一致 |
| 13 | lbpAttendDurationOld | GetLbpAttendDurationOld | verified | ✅ 修复格式化逻辑 - 确保0值也格式化与PHP一致 |
| 14 | inclassTest | GetInclassTest | verified | ✅ 验证通过 |
| 15 | oralQuestion | GetOralQuestion | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配 |
| 16 | homework | GetHomeworkData | verified | ✅ 验证通过 |
| 17 | similarHomework | GetHomeworkLikeData | verified | ✅ 验证通过 |
| 18 | exercise | GetExerciseColumn | verified | ✅ 验证通过 |
| 19 | exerciseAll | GetExerciseAllColumn | verified | ✅ 验证通过 |
| 20 | lbpInteractExam | GetLbpInteractExamColumn | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配 |
| 21 | mixPlaybackInteract | GetMixPlaybackInteract | verified | ✅ 验证通过 |
| 22 | littleKidFudaoHomeworkStatus | GetLittleKidFudaoData | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，默认值处理正确 |
| 23 | littleKidFudaoHomeworkLevel | GetLittleKidFudaoData | verified | ✅ 验证通过 - 与littleKidFudaoHomeworkStatus共享函数，逻辑等价、数据源一致、输出格式匹配，默认值处理正确 |
| 24 | littleKidFudaoInteract | GetLittleKidInteractData | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理无数据场景 |
| 25 | synchronousPractice | GetSynchronousPractice | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理无数据场景 |
| 26 | hasCompositionReport | GetHasCompositionReportData | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理作文报告数据聚合与格式化 |
| 27 | talk | GetTalk | verified | ✅ 修复完成 - talkCode字段输出已正确实现 |
| 28 | score | GetScoreData | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理默认值 |
| 29 | monthlyExamReport | GetMonthlyExamReportUrl | verified | ✅ 验证通过 - 逻辑等价、数据源一致、URL生成逻辑正确，正确处理配置检查和提交状态判断 |
| 30 | isInclassTeacherRoomAttend30minute | GetIsInclassTeacherRoomAttend30minute | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理到课30分钟判断 |
| 31 | isAttendFinish | GetIsAttendFinish | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理完课状态判断 |
| 32 | gjkAttendLessonLubo | GetGjkAttendLessonLubo | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理录播到课状态判断 |
| 33 | gjkCompleteLessonLubo | GetGjkCompleteLessonLubo | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理录播完课状态判断 |
| 34 | gjkLessonTag | GetGjkLessonTag | done | ✅ 修复完成 - 添加了studyPlanTag值转换逻辑，将数值(1,2,3)转换为文字描述("必看","选看","不看")，与PHP输出格式保持一致 |
| 35 | lpclessonName | GetLpcLessonName | done | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理章节名称格式化 |
| 36 | teacherName | GetLpcTeacherName | done | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配，正确处理教师名称映射 |
| 37 | attendStatus | GetLpcAttendStatus | done | ✅ 修复完成 - 创建了LPC专用常量(1,2,3)与PHP保持一致，修复了状态值不匹配问题 |
| 38 | finishStatus | GetLpcFinishStatus | done | ✅ 修复完成 - 使用LPC专用常量(1,2,3)与PHP保持一致，修复了完课状态值不匹配问题 |
| 39 | playStatus | GetLpcPlayStatus | done | ✅ 修复完成 - 使用LPC专用常量(1,2,3)与PHP保持一致，修复了回放状态值不匹配问题，正确处理300秒阈值 |
| 40 | preView | GetLpcPreViewData | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理LPC预习数据格式化 |
| 41 | tangtangExamStat | GetLpcTangTangExamStatData | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理堂堂测状态判断 |
| 42 | strengthPracticeStatus | GetLpcStrengthPracticeData | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理巩固练习状态判断，支持年级分类和完整的状态流转 |
| 43 | lessonReportUrl | GetLpcLessonReportData | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理时间判断、报告数据获取和短链接生成 |
| 44 | deerEloquenceHomeworkLevel | GetDeerEloquenceHomeworkLevel | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理小鹿口才作业等级映射(1→S, 2→A, 3→B)，无数据时返回'-' |
| 45 | deerProgrammingHomeworkLevel | GetDeerProgrammingHomeworkLevel | done | ✅ 迁移完成 - 逻辑等价、数据源复用、输出格式匹配，正确处理小鹿编程作业等级映射(1→S, 2→A, 3→B)，无数据时返回'-' |
| 46 | deerLessonReportUrl | GetDeerLessonReport | done | ✅ 迁移完成 - 逻辑等价、数据源一致、输出格式匹配，正确处理章节作品提交状态判断和报告URL生成 |
| 47 | deerLessonHomeWork | GetLessonHomeWork |  |  |
| 48 | zhiboLessonReportUrl | GetZhiboLessonReport |  |  |

# 方舟规则清单
| num | key | funcName | status | comment |
|------|--------|----------|----------|--------------|
| 49 | microphone |  |  |  |
| 50 | playbackOnlineTime |  |  |  |
| 51 | praise |  |  |  |
| 52 | rewardNum |  |  |  |
| 53 | deerAIContentTime |  |  |  |
| 54 | isAIAttendLesson |  |  |  |
| 55 | isAILessonOverclass |  |  |  |
| 56 | deerLpcBcHomeworkStatus |  |  |  |
| 57 | inclass_teacher_room_total_playback_content_time |  |  |  |
| 58 | is_inclass_teacher_room_content_view_10s |  |  |  |
| 59 | is_inclass_teacher_room_content_view_finish_85percent |  |  |  |
| 60 | isOverallFinish |  |  |  |
| 61 | playContentTime |  |  |  |
| 62 | contractPlayContentTime |  |  |  |
| 63 | is_contract_inclass_teacher_room_content_view_10s_contract |  |  |  |
| 64 | is_contract_inclass_teacher_room_content_view_finish_85percent_contract |  |  |  |
| 65 | isContractOverallFinish |  |  |  |
| 66 | is_contract_inclass_teacher_room_attend_5minute |  |  |  |
| 67 | is_contract_inclass_teacher_room_attend_finish |  |  |  |
| 68 | isContractInclassTeacherRoomContentView5Mintue |  |  |  |
| 69 | is_contract_inclass_teacher_room_content_view_finish_three_four |  |  |  |
| 70 | inclassDuration |  |  |  |
| 71 | inclassHasFinish |  |  |  |
| 72 | lbpPlayContentTime |  |  |  |
| 73 | preciseExercisesStatus |  |  |  |
| 74 | inclassTeacherRoomAttendDuration |  |  |  |
| 75 | inclassTeacherRoomTotalPlaybackContentTime |  |  |  |
| 76 | isInclassTeacherRoomAttendOrContentViewThreeFour |  |  |  |
| 77 | isInclassTeacherRoomAttendOrContentView30minute |  |  |  |
| 78 | isInclassTeacherRoomAttendFinish |  |  |  |
| 79 | interactiveCnt |  |  |  |
| 80 | inclassMultyLinkCnt |  |  |  |
| 81 | inclassChatCnt |  |  |  |