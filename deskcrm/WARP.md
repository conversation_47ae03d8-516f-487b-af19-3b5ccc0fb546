# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Overview

This is a Go-based microservice called `deskcrm` (工作台CRM模块) built using the Gin web framework. It follows a standard microservice architecture with HTTP APIs, message queue consumers, and scheduled tasks.

## Common Development Commands

### Build and Run

```bash
# Build the application
make

# Run the application (default starts HTTP server)
go run main.go

# Run with specific command
go run main.go cron <job-name>     # Run cron job
go run main.go toolkit <tool-name>  # Run development toolkit

# Build Docker image (CI/CD)
# The build process is handled by GitLab CI when commit message starts with "ci " or pushed to master/dev
```

### Testing

```bash
# Run unit tests for all packages
make test

# Run tests with coverage
make test-cover

# Run tests for a specific package
go test ./api/mercury/...

# Run a single test function
go test -run TestGetConfigForString ./api/mercury/
```

### Code Quality

```bash
# Format all Go files
make fmt

# Check formatting (CI will fail if not formatted)
make fmt-check

# Run go vet for static analysis
make vet

# Run golangci-lint (comprehensive linting)
make lint

# Initialize git hooks for pre-commit checks
make init
```

### Dependency Management

```bash
# Update specific internal library
make update  # Updates git.zuoyebang.cc/mark/mklib to latest master

# Update all dependencies
go mod tidy

# Download dependencies
go mod download
```

### Utility Commands

```bash
# Generate wire dependency injection code
make wire

# Clean temporary files and build artifacts
make clean

# Display version information
make version

# Generate model from database (development toolkit)
go run main.go toolkit model-gen
```

## Architecture and Code Structure

### Service Initialization Flow

The service follows a structured initialization pattern:

1. **Main Entry** (`main.go`): Sets up Gin engine, initializes resources through helpers, registers probes, and starts HTTP server or executes commands via Cobra CLI.

2. **Resource Initialization** (`helpers/init.go`):
   - PreInit: Sets app name, loads configuration, initializes logging
   - InitResource: Initializes MySQL, Redis, RocketMQ, COS, connection pools, validators, and API clients
   - Separate initialization for cron jobs vs HTTP server

3. **Configuration Loading** (`conf/`):
   - **Mount configs** (environment-specific, configurable via config center):
     - `config.yaml`: Basic service configuration (log level, server port, pprof)
     - `resource.yaml`: Database, Redis, Elasticsearch, COS, RocketMQ configurations
     - `api.yaml`: External service API endpoints and timeouts
   - **App configs** (bundled with code):
     - `app.yaml`: Business-specific configuration that rarely changes

### API Client Pattern

All external service integrations follow a consistent client pattern in `api/` directory:

```go
// Each service has its own client with:
- NewClient() factory function
- Structured request/response types
- Retry and timeout configuration from api.yaml
- Error handling with components.Error types
```

Services integrate with internal platforms through standardized HTTP clients configured in `conf/mount/api.yaml`. Common patterns include using assistantdesk.zuoyebang.cc as a gateway/proxy for internal services.

### Router Organization

The routing layer (`router/`) is organized into three main components:

1. **HTTP Routes** (`router/http.go`): 
   - Groups all routes under `/deskcrm` prefix
   - Applies middleware (version headers, debug mode, error recovery)
   - Delegates to controller packages

2. **MQ Consumer** (`router/mq.go`):
   - Initializes RocketMQ consumers based on configuration
   - Registers message handlers through MQC (Message Queue Controller)

3. **Command Router** (`router/command.go`):
   - Manages CLI commands for cron jobs and toolkits
   - Initializes resources specific to non-HTTP workloads

### Message Queue Integration

The service uses RocketMQ for asynchronous processing:
- Configuration in `resource.yaml` under `rmq.consumer` and `rmq.producer`
- Consumer handlers registered in `router/mq.go`
- MQ controller pattern in `libs/mqc/` for message routing

### Database Access

Multiple MySQL databases are configured for different business domains:
- `bzr_fudao`: Homework tutoring data
- `duxuesc`: Live streaming education data
- `lpcactive`: Learning plan activity data

Each database connection is managed with connection pooling and timeout configurations.

### Testing Infrastructure

Tests follow Go conventions with `*_test.go` files. Test initialization typically:
1. Sets up test environment variables
2. Initializes only required resources (MySQL, Redis, validators)
3. Uses `gin.CreateTestContext` for HTTP handler testing
4. Provides helper functions for common test scenarios

### Error Handling

The service uses a centralized error handling approach:
- Custom error types in `components/` package
- Consistent error responses via `base.RenderJson*` methods
- Error recovery middleware for production environments

### Environment Management

The service recognizes different environments through `git.zuoyebang.cc/pkg/golib/v2/env`:
- Development: Enables debug logging, stdout output
- Online/Production: Enables error recovery middleware, disables debug features

## Key Dependencies

- **Web Framework**: gin-gonic/gin (custom fork at git.zuoyebang.cc/pkg/gin)
- **Database**: gorm.io/gorm with MySQL driver
- **Cache**: Redis via gomodule/redigo
- **Message Queue**: Apache RocketMQ (custom fork)
- **Object Storage**: Tencent COS, Baidu BOS
- **Search**: Elasticsearch v7 via olivere/elastic
- **Internal Libraries**: git.zuoyebang.cc/pkg/golib/v2 (common utilities)

## Development Tips

1. **API Standards**: Follow the routing conventions in `router/README.md` - use lowercase, hyphens for separation, no file extensions in URIs.

2. **Configuration Management**: Environment-specific configs go in `conf/mount/`, code-bundled configs in `conf/app/`.

3. **Git Hooks**: Run `make init` to set up pre-commit hooks for code quality checks.

4. **Docker Builds**: CI/CD automatically builds Docker images when commits are prefixed with "ci " or pushed to master/dev branches.

5. **Service Discovery**: Internal services are accessed through standardized service names (e.g., `{service}-svc.{namespace}:8080`).

6. **Error Responses**: Always use the standard error response format with error codes and messages, utilizing the helper methods from base package.
