package dataQuery

import (
	"deskcrm/api/dat"
	"deskcrm/api/dau"
	"deskcrm/api/learningplan"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLearningPlansData 获取学习计划数据
// 对应PHP中的 Api_LearningPlan::learningPlans 方法
func (s *Singleton) GetLearningPlansData(ctx *gin.Context, studentUid int64, courseId int64) (map[int64]int, error) {
	if studentUid <= 0 || courseId <= 0 {
		return make(map[int64]int), nil
	}

	client := learningplan.NewClient()
	resp, err := client.LearningPlans(ctx, studentUid, courseId)
	if err != nil {
		zlog.Warnf(ctx, "GetLearningPlansData failed: %v", err)
		return nil, err
	}

	// 如果没有学习计划或为空，返回空map
	if resp == nil || resp.<PERSON>Empty || len(resp.Plans) == 0 {
		return make(map[int64]int), nil
	}

	// 构建lessonId -> tiers的映射
	lessonTagMap := make(map[int64]int)
	for _, plan := range resp.Plans {
		for _, subLesson := range plan.SubLessons {
			lessonTagMap[subLesson.SubLessonId] = subLesson.Tiers
		}
	}

	return lessonTagMap, nil
}

// GetLessonTeacherMap 获取章节教师映射关系
// 对应PHP中的 Service_Data_GetTaskList::getLessonTeacherMap 方法
func (s *Singleton) GetLessonTeacherMap(ctx *gin.Context, lessonIds []int64) (map[int64]string, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]string), nil
	}

	// 1. 获取章节对应的教师UID
	lessonTeacherMap, err := dat.GetTeacherUidByLessonIdArr(ctx, lessonIds, []string{"lessonId", "teacherUid"})
	if err != nil {
		zlog.Warnf(ctx, "GetLessonTeacherMap GetTeacherUidByLessonIdArr failed: %v", err)
		return nil, err
	}

	// 2. 提取所有教师UID并转换类型
	teacherUids := make([]int, 0)
	for _, lessonTeacher := range lessonTeacherMap {
		teacherUids = append(teacherUids, lessonTeacher.TeacherUid)
	}

	// 3. 获取教师信息（包括教师姓名）
	teacherInfoMap := make(map[int]string)
	if len(teacherUids) > 0 {
		teacherInfos, err := dau.GetTeachersByUids(ctx, teacherUids, []string{"teacherUid", "teacherName"})
		if err != nil {
			zlog.Warnf(ctx, "GetLessonTeacherMap GetTeachersByUids failed: %v", err)
			return nil, err
		}

		for _, teacherInfo := range teacherInfos {
			teacherInfoMap[teacherInfo.TeacherUid] = teacherInfo.TeacherName
		}
	}

	// 4. 构建最终的lessonId -> teacherName映射
	result := make(map[int64]string)
	for lessonId, teacher := range lessonTeacherMap {
		if teacherName, exists := teacherInfoMap[teacher.TeacherUid]; exists && teacherName != "" {
			result[lessonId] = teacherName
		} else {
			result[lessonId] = "-"
		}
	}

	return result, nil
}
