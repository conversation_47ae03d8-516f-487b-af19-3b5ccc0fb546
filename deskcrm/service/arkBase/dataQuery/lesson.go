package dataQuery

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dataproxy"
	"deskcrm/api/jxreport"
	"deskcrm/api/writereport"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"slices"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLuData 获取课程学生的LU数据
func (s *Singleton) GetLuData(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]*dataproxy.GetLuDataResp, error) {
	if courseId == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetLuDataResp), nil
	}

	s.AddFields(ctx, DataSourceLu, []string{"lessonId", "trade_status", "main_department"})
	fields, err := s.<PERSON>Fields(ctx, DataSourceLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   []int64{courseId},
		StudentUids: []int64{studentUid},
		Fields:      fields,
	}

	data, err := client.GetListByCourseIdsStudentUids(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	// 兼容试卷关联数据
	err = s.compatibleExamRelationData(ctx, data)
	if err != nil {
		zlog.Warnf(ctx, "compatibleExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLuDataResp)
	for _, item := range data {
		if item.TradeStatus != 1 {
			continue
		}
		result[item.LessonId] = item
	}

	return result, nil
}

// 兼容试卷数据 total_num is_have 总数相关的字段信息
func (s *Singleton) compatibleExamRelationData(ctx *gin.Context, data []*dataproxy.GetLuDataResp) error {
	fields, err := s.GetFields(ctx, DataSourceLu)
	if err != nil {
		return err
	}
	if !slices.Contains(fields, "exam_answer") || len(data) == 0 {
		return nil
	}

	// 提取lessonIds
	lessonIds := make([]int64, 0, len(data))
	for _, item := range data {
		lessonIds = append(lessonIds, item.LessonId)
	}

	// 去重
	lessonIdsMap := make(map[int64]bool)
	uniqueLessonIds := make([]int64, 0)
	for _, id := range lessonIds {
		if !lessonIdsMap[id] {
			lessonIdsMap[id] = true
			uniqueLessonIds = append(uniqueLessonIds, id)
		}
	}

	// 定义关系类型
	relationTypes := []int64{
		BindTypePracticeInClass,
		BindTypePreview,
		BindTypeHomework,
		BindTypeHomeworkIlab,
		BindTypeStage,
		BindTypeTestInClass,
		BindTypePrimaryMathPreview,
		BindTypePosttestMore,
		BindTypeOralQuestion,
		BindTypeWordPractice,
		BindTypeWordLearn,
		BindTypeOutTest,
		BindTypeImproveTest,
		BindTypeSyntasTest,
		BindTypePractises,
		BindTypeInTest,
	}

	// 查询试卷关联数据
	relationMaps := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	if len(uniqueLessonIds) > 0 {
		s.AddFields(ctx, DataSourceExamRelation, []string{"total_num"})

		relationData, err := s.GetExamRelationData(ctx, uniqueLessonIds, RelationTypeLesson, relationTypes)
		if err != nil {
			zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
			return err
		}

		// 构建映射关系
		for bindId, relationMap := range relationData {
			if relationMaps[bindId] == nil {
				relationMaps[bindId] = make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
			}
			for relationType, examInfo := range relationMap {
				relationMaps[bindId][relationType] = examInfo
			}
		}

		// 处理数据
		for i, info := range data {
			lessonId := info.LessonId

			if info.ExamAnswer == nil {
				info.ExamAnswer = make(map[string]map[string]interface{})
			}

			// 互动题
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePracticeInClass] != nil {
				data[i].ExerciseTotalNum = relationMaps[lessonId][BindTypePracticeInClass].TotalNum
			}

			// 课前预习 小学预习 小学课前预习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePreview] != nil {
				data[i].PreviewTotalNum5 = relationMaps[lessonId][BindTypePreview].TotalNum
			}

			// 课后作业【巩固练习】
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeHomework] != nil {
				data[i].HomeworkPracticeTotalNum = relationMaps[lessonId][BindTypeHomework].TotalNum
			}

			// ilab巩固练习升级绑
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeHomeworkIlab] != nil {
				data[i].IlabHomeworkGeXingTotalNum = relationMaps[lessonId][BindTypeHomeworkIlab].TotalNum
			}

			// 阶段性测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeStage] != nil {
				data[i].StageTestExamTotalNum = relationMaps[lessonId][BindTypeStage].TotalNum
			}

			// 堂堂测
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeTestInClass] != nil {
				data[i].TangTangExamTotalNum = relationMaps[lessonId][BindTypeTestInClass].TotalNum
			}

			// 小学-数学-同步练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePrimaryMathPreview] != nil {
				data[i].SynchronousPracticeTotalNum = relationMaps[lessonId][BindTypePrimaryMathPreview].TotalNum
			}

			// 初高中预习测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePosttestMore] != nil {
				data[i].PreviewTotalNum13 = relationMaps[lessonId][BindTypePosttestMore].TotalNum
			}

			// 口述题
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeOralQuestion] != nil {
				data[i].HaveOralQuestion = relationMaps[lessonId][BindTypeOralQuestion].BindStatus
				data[i].OralQuestionTotalNum = relationMaps[lessonId][BindTypeOralQuestion].TotalNum
			}

			// 单词练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeWordPractice] != nil {
				data[i].WordpracticeTotalNum = relationMaps[lessonId][BindTypeWordPractice].TotalNum
			}

			// 单词学习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeWordLearn] != nil {
				data[i].WordlearnTotalNum = relationMaps[lessonId][BindTypeWordLearn].TotalNum
			}

			// 出门测试
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeOutTest] != nil {
				data[i].OutTestTotalNum = relationMaps[lessonId][BindTypeOutTest].TotalNum
			}

			// 提升训练
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeImproveTest] != nil {
				data[i].ImproveTotalNum = relationMaps[lessonId][BindTypeImproveTest].TotalNum
			}

			// 语法练习
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeSyntasTest] != nil {
				data[i].GrammarpracticeTotalNum = relationMaps[lessonId][BindTypeSyntasTest].TotalNum
			}

			// 练一练
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypePractises] != nil {
				data[i].PracticesTotalNum = relationMaps[lessonId][BindTypePractises].TotalNum
			}

			// 入门测
			if relationMaps[lessonId] != nil && relationMaps[lessonId][BindTypeInTest] != nil {
				data[i].InTestTotalNum = relationMaps[lessonId][BindTypeInTest].TotalNum
			}

			// 预习数据映射 - 根据mainDepartment动态选择对应的预习字段
			// 因小学和初高预习对应的examType不一致，此处对小学、初高预习进行了数据Mapping！
			if examType, exists := PreviewDepartmentMap[info.MainDepartment]; exists {
				switch examType {
				case PreviewDepartment5: // 小学预习
					data[i].IsPreviewFinish = info.IsPreviewFinish
					data[i].PreviewParticipateNum = info.PreviewParticipateNum
					data[i].PreviewCorrectNum = info.PreviewCorrectNum
					data[i].PreviewTotalNum = info.PreviewTotalNum5
				case PreviewDepartment13: // 初高中预习
					data[i].IsPreviewFinish = info.IsPreviewFinish
					data[i].PreviewParticipateNum = info.PreviewParticipateNum
					data[i].PreviewCorrectNum = info.PreviewCorrectNum
					data[i].PreviewTotalNum = info.PreviewTotalNum13
				}
			}
		}
	}

	return nil
}

// GetLuData 获取课程学生的公共LU数据
func (s *Singleton) GetCommonLuData(ctx *gin.Context, lessonIds []int64, studentUid int64) (map[int64]*dataproxy.GetCommonLuResp, error) {
	if len(lessonIds) == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetCommonLuResp), nil
	}

	s.AddFields(ctx, DataSourceCommonLu, []string{"lesson_id"})
	fields, err := s.GetFields(ctx, DataSourceCommonLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLuComonListByStudentLessonsParam{
		LessonIds:  lessonIds,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLuComonListByStudentLessons(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetCommonLuResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetExamRelationData 获取试卷绑定数据
func (s *Singleton) GetExamRelationData(ctx *gin.Context, bindIds []int64, bindType int, relationTypes []int64) (map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {
	if len(bindIds) == 0 || len(relationTypes) == 0 {
		return make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp), nil
	}

	s.AddFields(ctx, DataSourceExamRelation, []string{"bind_id", "relation_type", "bind_status"})
	fields, err := s.GetFields(ctx, DataSourceExamRelation)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()

	params := dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindIds:       bindIds,
		BindType:      bindType,
		RelationTypes: relationTypes,
		ExamTags:      []int64{0}, // 添加 examTags 参数，与 PHP 版本保持一致
		Fields:        fields,
	}

	data, err := client.GetListByBindIdsBindTypeRelationTypesExamTags(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range data {
		if result[item.BindId] == nil {
			result[item.BindId] = make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
		}
		result[item.BindId][item.RelationType] = item
	}

	return result, nil
}

func (s *Singleton) GetLearnReportByClueGradeIds(ctx *gin.Context, clueIdGradeIdMap map[string]int64) (getLessonListResp map[string]assistantdeskgo.LearnReportInfo, err error) {
	leadsInfoMap, err := assistantdeskgo.NewClient().GetLearnReportByClueGradeIds(ctx, clueIdGradeIdMap)
	if err != nil {
		zlog.Warnf(ctx, "GetPrivateLeadsData failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}

// GetLessonDataByLessonIds 获取章节数据
func (s *Singleton) GetLessonDataByLessonIds(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetLessonDataByLessonIdsResp, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp), nil
	}

	s.AddFields(ctx, DataSourceLesson, []string{"lessonId"})
	fields, err := s.GetFields(ctx, DataSourceLesson)
	if err != nil {
		return nil, err
	}

	data, err := dataproxy.NewClient().GetLessonDataByLessonIds(ctx, dataproxy.GetLessonDataByLessonIdsParam{
		LessonIds: lessonIds,
		Fields:    fields,
	})
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

func (s *Singleton) GetLessonStrengthPracticeStatus(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]int, error) {
	s.AddFields(ctx, DataSourceLpcLu, []string{"exam7"})
	lpcStudentData, err := s.GetLpcStudentData(ctx, courseId, studentUid)
	if err != nil {
		return nil, err
	}

	courseInfo, err := s.GetCourseInfo(ctx, courseId)
	if err != nil {
		return nil, err
	}
	gradeId := courseInfo.MainGradeId
	gradeStage := consts.GetStageByGrade(gradeId)

	result := make(map[int64]int)
	for lessonId, lu := range lpcStudentData {
		exam7Data := lu.Exam7

		// 计算巩固练习状态（对应PHP中getLessonStrengthPracticeStatus逻辑）
		practiceStatus := consts.PracticeStatusNoCommit
		if exam7Data != nil {
			if exam7Data.IsHave == 1 {
				if gradeStage == define.GradeStagePrimary || gradeStage == define.GradeStagePreschool {
					switch exam7Data.CorrectStatus {
					case 2:
						practiceStatus = consts.PracticeStatusWaitCorrect
					case 4:
						practiceStatus = consts.PracticeStatusWaitRecorrect
					case 5:
						practiceStatus = consts.PracticeStatusWaitRecommit
					case 6:
						practiceStatus = consts.PracticeStatusRecorrected
					default:
						practiceStatus = consts.PracticeStatusNoCommit
					}
				}
			} else {
				practiceStatus = consts.PracticeStatusNoCommit
			}
		}

		result[lessonId] = practiceStatus
	}

	return result, nil
}

// GetLessonReportData 获取课堂报告数据
// 对应PHP中的Api_Jx::getHxLessonReport方法
func (s *Singleton) GetLessonReportData(ctx *gin.Context, lessonIds []int64, studentUid int64) (map[int64][]string, error) {
	if len(lessonIds) == 0 || studentUid == 0 {
		return make(map[int64][]string), nil
	}

	client := jxreport.NewClient()
	result, err := client.GetLessonReportUrl(ctx, lessonIds, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonReportData failed: %v", err)
		return nil, err
	}

	lessonReportMap := make(map[int64][]string)
	for lessonId, url := range result {
		if lessonReportMap[lessonId] == nil {
			lessonReportMap[lessonId] = make([]string, 0)
		}
		lessonReportMap[lessonId] = append(lessonReportMap[lessonId], url.Url)
	}

	return lessonReportMap, nil
}

// GetDeerData 获取小鹿数据
// 对应PHP中的initDeerData方法
func (s *Singleton) GetDeerData(ctx *gin.Context, studentUid int64, lessonIds []int64) (map[int64]*models.DeerDataLUDeerSpecialLPC, error) {
	if studentUid <= 0 || len(lessonIds) == 0 {
		return make(map[int64]*models.DeerDataLUDeerSpecialLPC), nil
	}

	data, err := models.DeerDataLUDeerSpecialLPCDao.GetListByStudentLessons(ctx, studentUid, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetDeerData failed: %v", err)
		return nil, err
	}

	// 构建以lessonId为key的映射
	result := make(map[int64]*models.DeerDataLUDeerSpecialLPC)
	for i := range data {
		result[data[i].LessonId] = &data[i]
	}

	return result, nil
}

// GetWritereportData 获取报告URL模板数据
// 对应PHP中的Api_Writereport::getReportUrl方法
func (s *Singleton) GetWritereportData(ctx *gin.Context) (*writereport.ReportUrlData, error) {
	client := writereport.NewClient()
	data, err := client.GetReportUrl(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GetWritereportData failed: %v", err)
		return nil, err
	}
	return data, nil
}
