package dataQuery

import (
	"deskcrm/api/dataproxy"

	"github.com/gin-gonic/gin"
)

// GetLpcStudentData 获取LPC学生数据
// 对应PHP中的Assistant_Common_Service_DataService_Query_LessonStudentData::getLpcListByCourseStudent方法
func (s *Singleton) GetLpcStudentData(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]*dataproxy.GetLpcListByCourseStudentResp, error) {
	if courseId == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetLpcListByCourseStudentResp), nil
	}

	// 添加需要的字段
	s.AddFields(ctx, DataSourceLpcLu, []string{"lpc_uid", "course_id", "student_uid", "lesson_id", "leads_id"})
	fields, err := s.GetFields(ctx, DataSourceLpcLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLpcListByCourseStudentParam{
		CourseId:   courseId,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLpcListByCourseStudent(ctx, params)
	if err != nil {
		return nil, err
	}

	// 构建映射表 key: lessonId
	resultMap := make(map[int64]*dataproxy.GetLpcListByCourseStudentResp)
	for _, lpcData := range data {
		resultMap[lpcData.LessonId] = lpcData
	}

	return resultMap, nil
}
