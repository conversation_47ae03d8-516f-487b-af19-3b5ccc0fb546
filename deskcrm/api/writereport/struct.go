package writereport

// ReportUrlResponse 报告URL接口响应结构
type ReportUrlResponse struct {
	ErrNo int           `json:"errNo"`
	ErrMsg string       `json:"errMsg"`
	Data  ReportUrlData `json:"data"`
}

// ReportUrlData 报告URL数据结构
type ReportUrlData struct {
	LessonReportUrl  string `json:"lessonReportUrl"`  // 章节报告URL模板
	CorrectReportUrl string `json:"correctReportUrl"` // 批改报告URL模板
	UnitReportUrl    string `json:"unitReportUrl"`    // 单元报告URL模板
}

// WeekReportResponse 周报告接口响应结构
type WeekReportResponse struct {
	ErrNo int            `json:"errNo"`
	ErrMsg string        `json:"errMsg"`
	Data  WeekReportData `json:"data"`
}

// WeekReportData 周报告数据结构
type WeekReportData struct {
	WeekReport []WeekReportItem `json:"weekReport"`
}

// WeekReportItem 周报告项目
type WeekReportItem struct {
	Report int    `json:"report"` // 周次
	Url    string `json:"url"`    // 报告URL
}
