package writereport

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Writereport,
	}
	return c
}

const (
	getReportUrlAPI = "/writereport/api/url"
	getWeekReportAPI = "/writereport/api/list"
)

// GetReportUrl 获取报告链接
// 对应PHP中的Api_Writereport::getReportUrl方法
func (c *Client) GetReportUrl(ctx *gin.Context) (*ReportUrlData, error) {
	opts := base.HttpRequestOptions{
		Encode: base.EncodeForm,
	}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getReportUrlAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetReportUrl err:%v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp ReportUrlResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return &resp.Data, nil
}

// GetWeekReport 获取周报告
// 对应PHP中的Api_Writereport::getWeekReport方法
func (c *Client) GetWeekReport(ctx *gin.Context, courseId, studentUid int64) (*WeekReportData, error) {
	req := map[string]interface{}{
		"courseId":   courseId,
		"studentUid": studentUid,
	}
	opts := base.HttpRequestOptions{
		RequestBody: req,
		Encode:      base.EncodeForm,
	}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getWeekReportAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetWeekReport err:%v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp WeekReportResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return &resp.Data, nil
}
