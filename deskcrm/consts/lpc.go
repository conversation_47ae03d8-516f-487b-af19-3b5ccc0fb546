package consts

// LPC状态常量，对应PHP中Service_Page_DeskV1_Student_PerformanceV1的常量定义
const (
	LpcUndoneStatus  = 1 // 未完成状态 (对应PHP的UNDONE_STATUS)
	LpcSuccessStatus = 2 // 成功状态 (对应PHP的SUCCESS_STATUS)
	LpcWaitStatus    = 3 // 等待状态 (对应PHP的WAIT_STATUS)
)

// LPC状态映射，对应PHP中的COURSE_STATUS数组
var LpcStatusMap = map[int]string{
	LpcUndoneStatus:  "未完成",
	LpcSuccessStatus: "已完成",
	LpcWaitStatus:    "未开始",
}

// LPC状态选项结构
type LpcStatusOption struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}

// GetLpcStatusOptions 获取LPC状态选项列表
func GetLpcStatusOptions() []LpcStatusOption {
	return []LpcStatusOption{
		{Label: LpcStatusMap[LpcUndoneStatus], Value: LpcUndoneStatus},
		{Label: LpcStatusMap[LpcSuccessStatus], Value: LpcSuccessStatus},
		{Label: LpcStatusMap[LpcWaitStatus], Value: LpcWaitStatus},
	}
}

// 巩固练习状态常量，对应PHP中AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_*
const (
	PracticeStatusNoHava        = 0 // 未布置
	PracticeStatusNoCommit      = 1 // 未提交
	PracticeStatusCommit        = 2 // 已提交
	PracticeStatusWaitCorrect   = 3 // 待批改
	PracticeStatusWaitRecorrect = 4 // 待重批
	PracticeStatusWaitRecommit  = 5 // 待重提
	PracticeStatusRecorrected   = 6 // 已订正
)

// 巩固练习状态映射
var PracticeStatusMap = map[int]string{
	PracticeStatusNoHava:        "未布置",
	PracticeStatusNoCommit:      "未提交",
	PracticeStatusCommit:        "已提交",
	PracticeStatusWaitCorrect:   "待批改",
	PracticeStatusWaitRecorrect: "待重批",
	PracticeStatusWaitRecommit:  "待重提",
	PracticeStatusRecorrected:   "已订正",
}

// 年级常量，对应PHP中的年级分类
const (
	GradeStagePrimary   = 1  // 小学
	GradeStagePreschool = 60 // 学前
)

// 小学年级列表，对应PHP中的getPrimaryGrade方法
var PrimaryGradeList = []int{1, 11, 12, 13, 14, 15, 16}

// 学前年级列表，对应PHP中的getPreGrade方法
var PreGradeList = []int{60, 61, 62, 63, 64}

// 巩固练习评级映射，对应PHP中的$practiceCorrectLevelMap
var PracticeCorrectLevelMap = map[int]string{
	1: "S",
	2: "A",
	3: "B",
}

// 小鹿作业等级映射，对应PHP中的$deerHomeworkLevelMap
var DeerHomeworkLevelMap = map[int64]string{
	1: "S",
	2: "A",
	3: "B",
}

// GetDeerHomeworkLevel 获取小鹿作业等级字符串
// 对应PHP中的self::$deerHomeworkLevelMap[$level] ?? '-'
func GetDeerHomeworkLevel(level int64) string {
	if levelStr, exists := DeerHomeworkLevelMap[level]; exists {
		return levelStr
	}
	return "-"
}
