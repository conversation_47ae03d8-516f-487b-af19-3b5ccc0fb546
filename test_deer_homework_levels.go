package main

import (
	"context"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc"
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
)

// TestDeerHomeworkLevels 测试小鹿作业等级相关函数
func TestDeerHomeworkLevels() {
	// 初始化Gin上下文
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 测试数据
	studentUid := int64(12345)
	lessonIds := []int64{1001, 1002, 1003}
	courseId := int64(5001)

	fmt.Println("=== 测试小鹿作业等级函数 ===")

	// 1. 测试常量映射
	fmt.Println("\n1. 测试常量映射:")
	testLevels := []int64{0, 1, 2, 3, 4}
	for _, level := range testLevels {
		result := consts.GetDeerHomeworkLevel(level)
		fmt.Printf("Level %d -> %s\n", level, result)
	}

	// 2. 测试数据源方法
	fmt.Println("\n2. 测试数据源方法:")
	dataQuerySingleton := &dataQuery.Singleton{}
	
	// 测试GetDeerData方法
	deerData, err := dataQuerySingleton.GetDeerData(ctx, studentUid, lessonIds)
	if err != nil {
		fmt.Printf("GetDeerData error: %v\n", err)
	} else {
		fmt.Printf("GetDeerData success, found %d records\n", len(deerData))
		for lessonId, data := range deerData {
			if data != nil {
				fmt.Printf("  Lesson %d: EloquenceLevel=%d, ProgrammingLevel=%d\n", 
					lessonId, data.DeerEloquenceHomeworkLevel, data.DeerProgrammingHomeworkLevel)
			}
		}
	}

	// 3. 测试业务逻辑方法
	fmt.Println("\n3. 测试业务逻辑方法:")
	
	// 创建Format实例（需要根据实际项目结构调整）
	format := &lessonDataFunc.Format{
		// 这里需要根据实际的Format结构体初始化
		// param: &lessonDataFunc.Param{
		//     StudentUid: studentUid,
		//     LessonIDs:  lessonIds,
		//     CourseID:   courseId,
		// },
		// dataQueryPoint: dataQuerySingleton,
	}

	// 测试GetDeerEloquenceHomeworkLevel
	fmt.Println("\n3.1 测试GetDeerEloquenceHomeworkLevel:")
	err = format.GetDeerEloquenceHomeworkLevel(ctx)
	if err != nil {
		fmt.Printf("GetDeerEloquenceHomeworkLevel error: %v\n", err)
	} else {
		fmt.Println("GetDeerEloquenceHomeworkLevel success")
	}

	// 测试GetDeerProgrammingHomeworkLevel
	fmt.Println("\n3.2 测试GetDeerProgrammingHomeworkLevel:")
	err = format.GetDeerProgrammingHomeworkLevel(ctx)
	if err != nil {
		fmt.Printf("GetDeerProgrammingHomeworkLevel error: %v\n", err)
	} else {
		fmt.Println("GetDeerProgrammingHomeworkLevel success")
	}

	fmt.Println("\n=== 测试完成 ===")
}

// TestDeerDataModel 测试小鹿数据模型
func TestDeerDataModel() {
	fmt.Println("\n=== 测试小鹿数据模型 ===")
	
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	
	studentUid := int64(12345)
	lessonIds := []int64{1001, 1002}
	
	// 测试DAO方法
	data, err := models.DeerDataLUDeerSpecialLPCDao.GetListByStudentLessons(ctx, studentUid, lessonIds)
	if err != nil {
		fmt.Printf("GetListByStudentLessons error: %v\n", err)
	} else {
		fmt.Printf("GetListByStudentLessons success, found %d records\n", len(data))
		for _, record := range data {
			fmt.Printf("  Record: LessonId=%d, StudentUid=%d, EloquenceLevel=%d, ProgrammingLevel=%d\n",
				record.LessonId, record.StudentUid, 
				record.DeerEloquenceHomeworkLevel, record.DeerProgrammingHomeworkLevel)
		}
	}
}

// TestEdgeCases 测试边界情况
func TestEdgeCases() {
	fmt.Println("\n=== 测试边界情况 ===")
	
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	
	dataQuerySingleton := &dataQuery.Singleton{}
	
	// 测试空参数
	fmt.Println("\n1. 测试空参数:")
	
	// 空学生ID
	deerData1, err1 := dataQuerySingleton.GetDeerData(ctx, 0, []int64{1001})
	fmt.Printf("Empty studentUid: len=%d, err=%v\n", len(deerData1), err1)
	
	// 空章节ID列表
	deerData2, err2 := dataQuerySingleton.GetDeerData(ctx, 12345, []int64{})
	fmt.Printf("Empty lessonIds: len=%d, err=%v\n", len(deerData2), err2)
	
	// 测试常量边界值
	fmt.Println("\n2. 测试常量边界值:")
	testValues := []int64{-1, 0, 1, 2, 3, 4, 999}
	for _, val := range testValues {
		result := consts.GetDeerHomeworkLevel(val)
		fmt.Printf("Level %d -> '%s'\n", val, result)
	}
}

func main() {
	// 运行所有测试
	TestDeerHomeworkLevels()
	TestDeerDataModel()
	TestEdgeCases()
}
